{"name": "tibco-bw-to-spring-boot-cli", "version": "1.0.0", "description": "CLI tool to convert TIBCO BusinessWorks processes to Spring Boot applications", "main": "dist/cli/index.js", "bin": {"tibco-converter": "dist/cli/index.js"}, "scripts": {"build": "tsc", "dev": "ts-node src/cli/index.ts", "start": "node dist/cli/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["tibco", "businessworks", "spring-boot", "migration", "cli", "converter"], "author": "Your Name", "license": "MIT", "dependencies": {"commander": "^11.1.0", "xml2js": "^0.6.2", "fast-xml-parser": "^4.3.2", "handlebars": "^4.7.8", "chalk": "^4.1.2", "ora": "^5.4.1", "inquirer": "^8.2.6", "fs-extra": "^11.1.1", "path": "^0.12.7", "lodash": "^4.17.21"}, "devDependencies": {"@types/node": "^20.8.0", "@types/xml2js": "^0.4.14", "@types/handlebars": "^4.1.0", "@types/inquirer": "^9.0.7", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.14.200", "@types/jest": "^29.5.5", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2", "prettier": "^3.0.3", "rimraf": "^5.0.5"}, "engines": {"node": ">=16.0.0"}}